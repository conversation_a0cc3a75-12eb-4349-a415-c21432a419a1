# Airflow LLM Service Mapper Project

A pipeline using Apache Airflow 3.0 to map WSDL/XSD defined services to OpenAPI specifications, leveraging Large Language Models (LLMs) and machine learning techniques.

## 🚀 Features

### Core Capabilities
- **Service Definition Ingestion**: Automated parsing of WSDL, XSD, and OpenAPI specifications
- **ML Training Pipeline**: End-to-end training of Hugging Face transformer models
- **Operational Mapping**: Real-time service mapping with ML models and heuristic fallbacks
- **Performance Monitoring**: Continuous monitoring with automated retraining triggers
- **Data Lineage**: Complete data lineage tracking using Airflow Datasets
- **Scalable Architecture**: Containerized deployment with horizontal scaling support
- **🆕 Dual Deployment Options**: Full Airflow pipeline OR standalone executable

### Technical Features
- **Airflow 3.0 TaskFlow API**: Modern DAG development with decorators
- **Hugging Face Integration**: Fine-tuning of transformer models (T5, BERT, etc.)
- **PEFT Support**: Parameter-efficient fine-tuning with LoRA adapters
- **Comprehensive Logging**: Structured logging throughout the pipeline
- **Error Handling**: Robust error handling with retry mechanisms
- **Configuration Management**: Centralized configuration with validation
- **🆕 Containerized Deployment**: Docker support for both Airflow and standalone modes
- **🧹 Data Management**: Comprehensive cleanup utilities for parsed data management

## 📁 Project Structure

```
airflow-llm-service-mapper/
├── 🔄 airflow/                          # Airflow-specific components
│   ├── dags/                            # Airflow DAGs
│   │   ├── 01_ingest_and_parse_definitions_dag.py
│   │   ├── 02_generate_training_data_dag.py
│   │   ├── 03_train_ml_model_dag.py
│   │   ├── 04_operational_mapping_pipeline_dag.py
│   │   ├── 05_monitoring_and_retraining_trigger_dag.py
│   │   └── 06_ingest_operational_logs_dag.py
│   ├── plugins/                         # Airflow plugins
│   ├── docs/                            # Airflow documentation
│   ├── docker-compose.yaml              # Airflow deployment
│   ├── Dockerfile                       # Airflow custom image
│   └── requirements.txt                 # Airflow dependencies
├── ⚡ standalone/                        # Standalone components
│   ├── src/                             # Source code
│   │   ├── standalone_ingest_parser.py  # Main executable
│   │   ├── constants.py                 # Configuration
│   │   ├── data_parsers.py              # WSDL/OpenAPI parsers
│   │   └── validation_utils.py          # Data validation
│   ├── tests/                           # Standalone tests
│   ├── docs/                            # Standalone documentation
│   ├── examples/                        # Usage examples
│   ├── Dockerfile                       # Standalone deployment
│   ├── docker-compose.yaml              # Standalone deployment
│   ├── requirements.txt                 # Standalone dependencies
│   └── run_parser.sh                    # Convenience script
├── 🔗 shared/                           # Shared components
│   ├── common/                          # Common modules (used by both)
│   ├── sample_data/                     # Sample WSDL/OpenAPI files
│   └── schemas/                         # Data schemas
├── 📁 data/                             # Data storage
├── 🤖 models/                           # ML models
├── 📚 docs/                             # Project documentation
└── 🛠️ scripts/                          # Utility scripts
```

## 🚀 Quick Start

**New to the project?** Start with our [**Getting Started Guide**](GETTING_STARTED.md) for step-by-step instructions!

### 🎯 Choose Your Path

| Need | Recommended | Setup Time | Guide |
|------|-------------|------------|-------|
| **Quick conversion** | Standalone | 5 minutes | [Standalone Guide](standalone/docs/README_standalone.md) |
| **CI/CD integration** | Standalone | 10 minutes | [Docker Guide](standalone/docs/DEPLOYMENT_OPTIONS.md) |
| **Production pipeline** | Airflow | 30 minutes | [Airflow Guide](airflow/docs/README_AIRFLOW.md) |
| **ML training** | Airflow | 45 minutes | [DAG Reference](airflow/docs/DAG_REFERENCE.md) |

### ⚡ 5-Minute Standalone Start
```bash
cd standalone/
pip install -r requirements.txt
python src/standalone_ingest_parser.py \
  --service-name sample_service \
  --wsdl-uri ../shared/sample_data/wsdl_input/todo-service.wsdl \
  --openapi-uri ../shared/sample_data/osdmp_target/todo-api.yaml
```

### 🔄 30-Minute Airflow Start
```bash
cd airflow/
docker-compose up -d
# Access UI at http://localhost:8080 (admin/admin)
```

**Need help choosing?** See our [**Deployment Comparison**](docs/DEPLOYMENT_COMPARISON.md)

## 📊 Deployment Comparison

| Feature | Standalone | Airflow Pipeline |
|---------|------------|------------------|
| **Setup Time** | 2 minutes | 30+ minutes |
| **Dependencies** | 4 packages | Full Airflow stack |
| **Memory Usage** | ~50MB | ~500MB+ |
| **Execution** | Immediate | Scheduled |
| **Monitoring** | Log files | Airflow UI |
| **Scalability** | Single process | Distributed workers |
| **Best For** | Dev, CI/CD, Edge | Production pipelines |
| **Docker Support** | ✅ Lightweight | ✅ Full stack |

## 🐳 Docker Usage

### Standalone Docker Examples

```bash
cd standalone/

# 1. Build image
docker build -t standalone-parser:latest .

# 2. Basic usage
docker run --rm standalone-parser:latest python src/standalone_ingest_parser.py --help

# 3. With volume mounts
docker run --rm \
  -v /host/input:/app/data:ro \
  -v /host/output:/app/output:rw \
  standalone-parser:latest \
  python src/standalone_ingest_parser.py \
    --service-name my_service \
    --wsdl-uri /app/data/service.wsdl \
    --openapi-uri /app/data/api.yaml \
    --output-base-path /app/output

# 4. Using docker-compose
docker-compose run parser python src/standalone_ingest_parser.py --help
docker-compose run parser-sample  # Process sample data
docker-compose run parser-shell   # Interactive shell

# 5. Batch processing
docker run --rm \
  -v /host/data:/app/data:ro \
  -v /host/output:/app/output:rw \
  standalone-parser:latest \
  bash -c "
    for service in service1 service2 service3; do
      python src/standalone_ingest_parser.py \
        --service-name \$service \
        --wsdl-uri /app/data/\$service.wsdl \
        --openapi-uri /app/data/\$service.yaml \
        --output-base-path /app/output
    done
  "
```

### Volume Mount Points

| Mount Point | Purpose | Access |
|-------------|---------|--------|
| `/app/data` | Input WSDL/OpenAPI files | Read-only |
| `/app/output` | Parsed output files | Read-write |
| `/app/logs` | Log files | Read-write |
| `/app/sample_data` | Sample data (optional) | Read-only |

## 📚 Documentation

### 🚀 Getting Started
- **[GETTING_STARTED.md](GETTING_STARTED.md)**: Universal quick start guide
- **[DEPLOYMENT_COMPARISON.md](docs/DEPLOYMENT_COMPARISON.md)**: Standalone vs Airflow comparison

### ⚡ Standalone Parser
- **[standalone/docs/README_standalone.md](standalone/docs/README_standalone.md)**: Complete usage guide
- **[standalone/docs/QUICK_REFERENCE.md](standalone/docs/QUICK_REFERENCE.md)**: Quick reference card
- **[standalone/docs/CHANGELOG_STANDALONE.md](standalone/docs/CHANGELOG_STANDALONE.md)**: Version history
- **[standalone/examples/](standalone/examples/)**: Usage examples

### 🔄 Airflow Pipeline
- **[airflow/docs/README_AIRFLOW.md](airflow/docs/README_AIRFLOW.md)**: Airflow-specific overview
- **[airflow/docs/DEPLOYMENT_GUIDE.md](airflow/docs/DEPLOYMENT_GUIDE.md)**: Complete deployment guide
- **[airflow/docs/DAG_REFERENCE.md](airflow/docs/DAG_REFERENCE.md)**: Detailed DAG documentation
- **[airflow/docs/CONFIGURATION_GUIDE.md](airflow/docs/CONFIGURATION_GUIDE.md)**: Configuration management
- **[airflow/docs/TROUBLESHOOTING.md](airflow/docs/TROUBLESHOOTING.md)**: Common issues and solutions

### 📋 Project Documentation
- **[docs/DEPLOYMENT_ARCHITECTURE_GUIDE.md](docs/DEPLOYMENT_ARCHITECTURE_GUIDE.md)**: System architecture
- **[docs/SAMPLE_DATA_PRODUCT_OVERVIEW.md](docs/SAMPLE_DATA_PRODUCT_OVERVIEW.md)**: Sample data overview
- **[docs/SAMPLE_DATA_TECHNICAL_GUIDE.md](docs/SAMPLE_DATA_TECHNICAL_GUIDE.md)**: Technical sample data guide

### 🔗 Shared Resources
- **[shared/sample_data/](shared/sample_data/)**: Sample WSDL/OpenAPI files
- **[shared/common/](shared/common/)**: Shared modules and utilities

## 🧪 Testing

### Comprehensive Test Suite
```bash
# Install test dependencies
pip install -r requirements-test.txt

# Run all tests
pytest

# Run specific test categories
pytest -m unit                    # Unit tests only
pytest -m integration            # Integration tests only
pytest -m airflow                # Airflow-specific tests
pytest -m standalone             # Standalone-specific tests

# Run with coverage
pytest --cov=shared --cov=standalone/src --cov-report=html
```

### Standalone Testing
```bash
cd standalone/
python tests/test_standalone_parser.py
./examples/basic_usage.sh
./examples/docker_usage.sh
```

### Airflow Testing
```bash
cd airflow/
docker-compose exec airflow-webserver python -m pytest tests/
```

### Test Structure
```
tests/
├── unit/                         # Unit tests
│   ├── test_shared_common/       # Shared module tests
│   ├── test_standalone/          # Standalone tests
│   └── test_airflow_dags/        # Airflow DAG tests
├── integration/                  # Integration tests
└── conftest.py                   # Test configuration
```

## 🧹 Data Management & Cleanup

The project includes comprehensive cleanup utilities for managing parsed service definition data:

### Quick Cleanup Commands
```bash
# Show current data summary
./cleanup.sh summary

# Clean all parsed data (with confirmation)
./cleanup.sh clean-all

# Clean specific services
./cleanup.sh clean-services old_service test_service

# Keep only latest 3 versions of each service
./cleanup.sh clean-old --keep 3

# Remove orphaned data not in catalog
./cleanup.sh clean-orphaned

# Preview changes without deleting (dry run)
./cleanup.sh clean-all --dry-run
```

### Cleanup Features
- **Multiple cleanup strategies**: All data, specific services, old versions, orphaned data
- **Safety features**: Confirmation prompts, dry run mode, detailed logging
- **Space reporting**: Shows how much disk space will be freed
- **Catalog management**: Automatically updates service catalogs
- **Cross-platform**: Works with both standalone and Airflow environments

📖 **Full Guide**: See [**Cleanup Guide**](CLEANUP_GUIDE.md) for complete documentation

## 🔧 Development

### Standalone Development
```bash
cd standalone/
# Edit source files in src/
# Test changes
python tests/test_standalone_parser.py
# Build Docker image
docker build -t standalone-parser:dev .
```

### Airflow Development
```bash
cd airflow/
# Edit DAGs in dags/
# Restart services
docker-compose restart airflow-webserver airflow-scheduler
```

## 🚀 Production Deployment

### Standalone Production
- **Kubernetes**: Deploy as a Job or CronJob
- **CI/CD**: Integrate into pipelines
- **Edge Computing**: Lightweight deployment
- **Batch Processing**: Process multiple services

### Airflow Production
- **Cloud Managed**: AWS MWAA, Google Cloud Composer
- **Kubernetes**: Helm charts available
- **Self-hosted**: Production docker-compose setup

## ❓ FAQ

### Quick Help
- **[FAQ](FAQ.md)**: Answers to common questions
- **[Troubleshooting](airflow/docs/TROUBLESHOOTING.md)**: Common issues and solutions
- **[Getting Started](GETTING_STARTED.md)**: Step-by-step setup guide

---

## 📈 Project Status

- ✅ **Production Ready**: Both standalone and Airflow deployments
- 🧪 **Well Tested**: Comprehensive test suite with 85%+ coverage
- 📚 **Well Documented**: Complete documentation for all use cases
- 🔄 **Actively Maintained**: Regular updates and improvements
- 🤝 **Community Driven**: Open to contributions and feedback

**Ready to transform your SOAP services to REST APIs? [Get started now!](GETTING_STARTED.md)** 🚀
