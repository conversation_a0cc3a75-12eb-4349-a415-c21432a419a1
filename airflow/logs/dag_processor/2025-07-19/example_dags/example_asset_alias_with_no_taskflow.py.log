{"timestamp":"2025-07-19T18:05:41.057393","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_asset_alias_with_no_taskflow.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:06:11.092782","level":"error","event":"Process timed out, PID: 69","logger":"airflow.utils.timeout.TimeoutPosix"}
{"timestamp":"2025-07-19T18:06:11.101330","level":"error","event":"Failed to import: /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_asset_alias_with_no_taskflow.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"AirflowTaskTimeout","exc_value":"DagBag import timeout for /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_asset_alias_with_no_taskflow.py after 30.0s.\nPlease take a look at these docs to improve your DAG import time:\n* https://airflow.apache.org/docs/apache-airflow/3.0.2/best-practices.html#top-level-python-code\n* https://airflow.apache.org/docs/apache-airflow/3.0.2/best-practices.html#reducing-dag-complexity, PID: 69","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_asset_alias_with_no_taskflow.py","lineno":54,"name":"<module>"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/definitions/asset/__init__.py","lineno":390,"name":"__init__"},{"filename":"<attrs generated methods airflow.sdk.definitions.asset.Asset>","lineno":19,"name":"__attrs_init__"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/definitions/asset/__init__.py","lineno":182,"name":"_sanitize_uri"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/sdk/definitions/asset/__init__.py","lineno":137,"name":"_get_uri_normalizer"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers_manager.py","lineno":1256,"name":"asset_uri_handlers"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers_manager.py","lineno":357,"name":"wrapped_function"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers_manager.py","lineno":504,"name":"initialize_providers_asset_uri_resources"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers_manager.py","lineno":832,"name":"_discover_asset_uri_resources"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers_manager.py","lineno":820,"name":"_safe_register_resource"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers_manager.py","lineno":306,"name":"_correctness_check"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/utils/module_loading.py","lineno":39,"name":"import_string"},{"filename":"/usr/local/lib/python3.12/importlib/__init__.py","lineno":90,"name":"import_module"},{"filename":"<frozen importlib._bootstrap>","lineno":1387,"name":"_gcd_import"},{"filename":"<frozen importlib._bootstrap>","lineno":1360,"name":"_find_and_load"},{"filename":"<frozen importlib._bootstrap>","lineno":1331,"name":"_find_and_load_unlocked"},{"filename":"<frozen importlib._bootstrap>","lineno":935,"name":"_load_unlocked"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers/google/assets/gcs.py","lineno":21,"name":"<module>"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/providers/google/cloud/hooks/gcs.py","lineno":38,"name":"<module>"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/google/api_core/exceptions.py","lineno":29,"name":"<module>"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/google/rpc/error_details_pb2.py","lineno":21,"name":"<module>"},{"filename":"<frozen importlib._bootstrap>","lineno":1360,"name":"_find_and_load"},{"filename":"<frozen importlib._bootstrap>","lineno":1331,"name":"_find_and_load_unlocked"},{"filename":"<frozen importlib._bootstrap>","lineno":935,"name":"_load_unlocked"},{"filename":"<frozen importlib._bootstrap_external>","lineno":995,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap_external>","lineno":1146,"name":"get_code"},{"filename":"<frozen importlib._bootstrap_external>","lineno":1214,"name":"_cache_bytecode"},{"filename":"<frozen importlib._bootstrap_external>","lineno":1228,"name":"set_data"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/utils/timeout.py","lineno":69,"name":"handle_timeout"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:08:59.091888","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_asset_alias_with_no_taskflow.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:00.295166","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_asset_alias_with_no_taskflow.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:39.290978","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_asset_alias_with_no_taskflow.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:10.017751","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_asset_alias_with_no_taskflow.py","logger":"airflow.models.dagbag.DagBag"}
