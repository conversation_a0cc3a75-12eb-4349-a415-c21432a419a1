{"timestamp":"2025-07-19T18:07:24.356065","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:49.782080","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:20.543364","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:53.540632","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:03.325660","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:25.524257","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:15.500031","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:47.512781","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_latest_only_with_trigger.py","logger":"airflow.models.dagbag.DagBag"}
