{"timestamp":"2025-07-19T18:05:46.484799","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dag_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:09:04.475625","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dag_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:01.164964","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dag_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:39.558695","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dag_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:10.652427","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dag_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:45.482844","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dag_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:18.121042","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dag_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:48.763167","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dag_decorator.py","logger":"airflow.models.dagbag.DagBag"}
