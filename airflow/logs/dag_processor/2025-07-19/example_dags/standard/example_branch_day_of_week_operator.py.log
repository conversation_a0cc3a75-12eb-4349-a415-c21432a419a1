{"timestamp":"2025-07-19T18:07:51.580915","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_day_of_week_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:58.213448","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_day_of_week_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:32.425145","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_day_of_week_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:06.391243","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_day_of_week_operator.py","logger":"airflow.models.dagbag.DagBag"}
