{"timestamp":"2025-07-19T18:05:09.230635","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:08:21.947134","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:56.638528","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:29.199530","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:17:33.513183","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:19.434702","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:14.792023","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:46.447085","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
