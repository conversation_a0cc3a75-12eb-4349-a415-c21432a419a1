{"timestamp":"2025-07-19T18:07:25.583638","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:07:25.649160","level":"error","event":"Failed to import: /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'tests_common'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","lineno":60,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:15:58.482361","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:58.507369","level":"error","event":"Failed to import: /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'tests_common'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","lineno":60,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:34.281495","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:34.588065","level":"error","event":"Failed to import: /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'tests_common'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","lineno":60,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:18:06.742225","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:06.779998","level":"error","event":"Failed to import: /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'tests_common'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","lineno":60,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
