{"timestamp":"2025-07-19T18:05:15.516775","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_python_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:08:38.607733","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_python_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:51.772863","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_python_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:23.438484","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_python_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:17:09.247411","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_python_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:24.782116","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_python_decorator.py","logger":"airflow.models.dagbag.DagBag"}
