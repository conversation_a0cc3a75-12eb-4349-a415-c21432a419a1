{"timestamp":"2025-07-19T18:05:24.716847","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:08:49.954194","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:51.101771","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:22.388151","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:54.785178","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:04.205046","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
