{"timestamp":"2025-07-19T18:06:43.583723","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:09:24.991490","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:56.451860","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:28.332936","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:17:31.132120","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:23.878044","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:15.270845","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:47.334210","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
