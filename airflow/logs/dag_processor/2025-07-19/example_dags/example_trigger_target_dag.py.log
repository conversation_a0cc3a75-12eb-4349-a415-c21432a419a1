{"timestamp":"2025-07-19T18:06:45.335443","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_trigger_target_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:09:34.933517","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_trigger_target_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:56.012275","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_trigger_target_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:27.762290","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_trigger_target_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:17:30.184937","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_trigger_target_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:52.385048","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_trigger_target_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:23.081946","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_trigger_target_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:53.478208","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_trigger_target_dag.py","logger":"airflow.models.dagbag.DagBag"}
