{"timestamp":"2025-07-19T18:05:06.676454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:05:06.784671","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:08:12.253302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:08:12.782666","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:15:49.361482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:49.475064","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:20.346941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:20.382680","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:53.382751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:53.419935","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:18:03.081226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:03.166093","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:32:08.513121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:09.092336","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:13.195133","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:13.337417","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:44.305285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:44.363336","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
