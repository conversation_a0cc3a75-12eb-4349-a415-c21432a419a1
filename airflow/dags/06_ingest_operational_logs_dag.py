from __future__ import annotations

import pendulum
import json
import os
import glob
import re
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple

from airflow.decorators import dag, task
from airflow.models.param import Param
from airflow.models import Variable
from airflow.operators.python import get_current_context
from airflow.datasets import Dataset

# Import shared modules
import sys

# Add shared directory to Python path BEFORE importing shared modules
# In Docker container, shared is mounted at /opt/airflow/shared
shared_path = '/opt/airflow/shared'
if os.path.exists(shared_path) and shared_path not in sys.path:
    sys.path.insert(0, shared_path)  # Use insert(0, ...) for higher priority
else:
    # Fallback for local development (relative path)
    shared_path = os.path.join(os.path.dirname(__file__), '..', '..', 'shared')
    if os.path.exists(shared_path) and shared_path not in sys.path:
        sys.path.insert(0, shared_path)

# Now import shared modules
from shared.common import constants, data_parsers, validation_utils
from shared.common.definition_store import DefinitionStore

# Define dataset for lineage tracking
OPERATIONAL_LOGS_DS = Dataset(constants.OPERATIONAL_LOGS_DATASET_URI)

@dag(
    schedule="@daily",  # Can be adjusted based on log volume and processing needs
    start_date=pendulum.datetime(2024, 1, 1, tz="UTC"),
    catchup=False,
    tags=["llm-mapper", "logs", "operational-data", "training-data"],
    params={
        "service_name": Param("example_service", type="string", description="Name of the service to process logs for."),
        "soap_log_path": Param("logs/soap", type="string", description="Path to SOAP logs directory."),
        "rest_log_path": Param("logs/rest", type="string", description="Path to REST logs directory."),
        "log_date_range": Param("1", type="string", description="Number of days of logs to process (from today)."),
        "output_path": Param("data/operational_logs", type="string", description="Path to store processed logs."),
        "sample_rate": Param(1.0, type="number", description="Fraction of logs to sample (0.0-1.0)."),
        "anonymize_data": Param(True, type="boolean", description="Whether to anonymize sensitive data in logs."),
    },
    doc_md="""
    ### Ingest Operational Logs DAG
    
    This DAG ingests and processes operational logs from both SOAP and REST API calls.
    It extracts request/response pairs, matches them with API definitions, and prepares
    them for use in training ML models.
    
    - **Input**: Raw SOAP and REST API logs
    - **Output**: Processed and structured log data for training
    
    The DAG supports:
    - Log filtering by date range
    - Sampling to reduce volume
    - Anonymization of sensitive data
    - Matching logs to API definitions
    - Extracting request/response patterns
    """
)
def ingest_operational_logs_dag():

    @task
    def validate_service_exists(service_name: str) -> dict:
        """Check if the service exists in the catalog and return its metadata"""
        metadata = DefinitionStore.get_service_metadata(service_name)
        if not metadata:
            available_services = DefinitionStore.list_available_services()
            raise ValueError(f"Service '{service_name}' not found in catalog. Available services: {available_services}")
        return metadata

    @task
    def collect_soap_logs(soap_log_path: str, service_name: str, days_to_process: int, 
                          sample_rate: float, registry: dict, force_reprocess: bool) -> dict:
        """Collect and parse SOAP logs from the specified path"""
        # Calculate date range
        end_date = pendulum.now()
        start_date = end_date.subtract(days=days_to_process)
        
        print(f"Collecting SOAP logs for service '{service_name}' from {start_date.to_date_string()} to {end_date.to_date_string()}")
        
        # Get processed files and hashes from registry
        processed_files = set(registry.get("processed_files", {}).get("soap", []))
        file_hashes = registry.get("file_hashes", {})
        updated_hashes = {}
        
        # Find log files in the specified path
        log_files = []
        for pattern in ["*.log", "*.xml", "*.txt"]:
            log_files.extend(glob.glob(f"{soap_log_path}/**/{pattern}", recursive=True))
        
        print(f"Found {len(log_files)} potential SOAP log files")
        
        # Filter logs by date if possible
        filtered_logs = []
        for log_file in log_files:
            # Skip if already processed and not forcing reprocess
            if log_file in processed_files and not force_reprocess:
                # Check if file has changed since last processing
                current_hash = hash_file(log_file)
                if log_file in file_hashes and file_hashes[log_file] == current_hash:
                    continue
            
            # Try to extract date from filename or file metadata
            file_date = extract_date_from_filename(log_file)
            if not file_date:
                # If date can't be extracted from filename, use file modification time
                file_date = pendulum.from_timestamp(os.path.getmtime(log_file))
            
            if start_date <= file_date <= end_date:
                filtered_logs.append(log_file)
        
        print(f"Found {len(filtered_logs)} SOAP log files within date range")
        
        # Apply sampling if needed
        if sample_rate < 1.0:
            # Ensure deterministic sampling by using the same seed
            random.seed(service_name)
            filtered_logs = random.sample(filtered_logs, max(1, int(len(filtered_logs) * sample_rate)))
            print(f"Sampled down to {len(filtered_logs)} SOAP log files")
        
        # Process the filtered logs
        parsed_logs = []
        
        for log_file in filtered_logs:
            try:
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Calculate file hash for change detection
                file_hash = hashlib.md5(content.encode()).hexdigest()
                updated_hashes[log_file] = file_hash
                
                # Extract SOAP requests and responses
                soap_pairs = extract_soap_request_response_pairs(content)
                
                for pair in soap_pairs:
                    parsed_logs.append({
                        "source_file": log_file,
                        "timestamp": pair.get("timestamp", file_date.isoformat()),
                        "request": pair.get("request", ""),
                        "response": pair.get("response", ""),
                        "operation": pair.get("operation", ""),
                        "status": pair.get("status", "unknown")
                    })
            except Exception as e:
                print(f"Error processing SOAP log file {log_file}: {e}")
        
        # Update registry with processed files
        updated_processed_files = list(processed_files)
        updated_processed_files.extend([f for f in filtered_logs if f not in processed_files])
        
        # Return the results
        return {
            "logs": parsed_logs,
            "pair_count": len(parsed_logs),
            "file_count": len(filtered_logs),
            "registry": {
                "service_name": service_name,
                "processed_files": {"soap": updated_processed_files},
                "file_hashes": updated_hashes,
                "last_updated": pendulum.now().isoformat()
            }
        }

    @task
    def collect_rest_logs(rest_log_path: str, service_name: str, days_to_process: int, sample_rate: float) -> dict:
        """Collect and parse REST logs from the specified path"""
        # Calculate date range
        end_date = pendulum.now()
        start_date = end_date.subtract(days=days_to_process)
        
        print(f"Collecting REST logs for service '{service_name}' from {start_date.to_date_string()} to {end_date.to_date_string()}")
        
        # Find log files in the specified path
        log_files = []
        for pattern in ["*.log", "*.json", "*.txt"]:
            log_files.extend(glob.glob(f"{rest_log_path}/**/{pattern}", recursive=True))
        
        print(f"Found {len(log_files)} potential REST log files")
        
        # Filter logs by date if possible
        filtered_logs = []
        for log_file in log_files:
            # Try to extract date from filename or file metadata
            file_date = extract_date_from_filename(log_file)
            if not file_date:
                # If date can't be extracted from filename, use file modification time
                file_date = pendulum.from_timestamp(os.path.getmtime(log_file))
            
            if start_date <= file_date <= end_date:
                filtered_logs.append(log_file)
        
        print(f"Filtered to {len(filtered_logs)} REST log files within date range")
        
        # Sample logs if needed
        if sample_rate < 1.0:
            import random
            sample_size = max(1, int(len(filtered_logs) * sample_rate))
            filtered_logs = random.sample(filtered_logs, sample_size)
            print(f"Sampled {len(filtered_logs)} REST log files (rate: {sample_rate})")
        
        # Parse REST logs
        parsed_logs = []
        for log_file in filtered_logs:
            try:
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Extract REST requests and responses
                rest_pairs = extract_rest_request_response_pairs(content)
                
                for pair in rest_pairs:
                    parsed_logs.append({
                        "source_file": log_file,
                        "timestamp": pair.get("timestamp", file_date.isoformat()),
                        "method": pair.get("method", "GET"),
                        "path": pair.get("path", ""),
                        "request_headers": pair.get("request_headers", {}),
                        "request_body": pair.get("request_body", ""),
                        "response_status": pair.get("response_status", 200),
                        "response_headers": pair.get("response_headers", {}),
                        "response_body": pair.get("response_body", "")
                    })
            except Exception as e:
                print(f"Error processing REST log file {log_file}: {e}")
        
        print(f"Extracted {len(parsed_logs)} REST request/response pairs")
        
        return {
            "service_name": service_name,
            "log_type": "rest",
            "date_range": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            },
            "file_count": len(filtered_logs),
            "pair_count": len(parsed_logs),
            "logs": parsed_logs
        }

    @task
    def match_logs_with_definitions(soap_logs: dict, rest_logs: dict, validation_logs: dict, 
                                   service_name: str, anonymize: bool) -> dict:
        """Match log entries with API definitions and create training examples"""
        # Load service definitions
        definitions = DefinitionStore.load_service_definitions(service_name)
        
        matched_examples = []
        
        # Process SOAP logs
        if "wsdl" in definitions and soap_logs["pair_count"] > 0:
            wsdl_data = definitions["wsdl"]
            operations = {op["name"]: op for op in wsdl_data.get("operations", [])}
            
            for log_entry in soap_logs["logs"]:
                # Try to identify the operation from the SOAP request
                operation_name = log_entry.get("operation")
                if not operation_name:
                    operation_name = extract_soap_operation_name(log_entry["request"])
                
                # If we found a matching operation in the WSDL
                if operation_name in operations:
                    # Create a matched example
                    example = {
                        "source": "soap_log",
                        "service_name": service_name,
                        "timestamp": log_entry["timestamp"],
                        "operation_name": operation_name,
                        "wsdl_operation": operations[operation_name],
                        "request_xml": anonymize_xml(log_entry["request"]) if anonymize else log_entry["request"],
                        "response_xml": anonymize_xml(log_entry["response"]) if anonymize else log_entry["response"],
                        "status": log_entry["status"]
                    }
                    matched_examples.append(example)
        
        # Process REST logs
        if "openapi" in definitions and rest_logs["pair_count"] > 0:
            openapi_data = definitions["openapi"]
            spec = openapi_data.get("full_spec", openapi_data.get("extracted", {}))
            paths = spec.get("paths", {})
            
            for log_entry in rest_logs["logs"]:
                path = log_entry["path"]
                method = log_entry["method"].lower()
                
                # Try to match the path with OpenAPI paths
                matched_path, path_params = match_rest_path(path, list(paths.keys()))
                
                if matched_path and matched_path in paths and method in paths[matched_path]:
                    operation = paths[matched_path][method]
                    
                    # Create a matched example
                    example = {
                        "source": "rest_log",
                        "service_name": service_name,
                        "timestamp": log_entry["timestamp"],
                        "path": matched_path,
                        "method": method,
                        "path_params": path_params,
                        "openapi_operation": operation,
                        "request_headers": anonymize_headers(log_entry["request_headers"]) if anonymize else log_entry["request_headers"],
                        "request_body": anonymize_json(log_entry["request_body"]) if anonymize else log_entry["request_body"],
                        "response_status": log_entry["response_status"],
                        "response_headers": anonymize_headers(log_entry["response_headers"]) if anonymize else log_entry["response_headers"],
                        "response_body": anonymize_json(log_entry["response_body"]) if anonymize else log_entry["response_body"]
                    }
                    matched_examples.append(example)
        
        # Process validation logs
        if validation_logs["entry_count"] > 0:
            for log_entry in validation_logs["logs"]:
                # Validation logs are already structured for training
                if anonymize:
                    if "soap_request" in log_entry:
                        log_entry["soap_request"] = anonymize_xml(log_entry["soap_request"])
                    if "soap_response" in log_entry:
                        log_entry["soap_response"] = anonymize_xml(log_entry["soap_response"])
                    if "rest_request_body" in log_entry:
                        log_entry["rest_request_body"] = anonymize_json(log_entry["rest_request_body"])
                    if "rest_response_body" in log_entry:
                        log_entry["rest_response_body"] = anonymize_json(log_entry["rest_response_body"])
                    if "rest_request_headers" in log_entry:
                        log_entry["rest_request_headers"] = anonymize_headers(log_entry["rest_request_headers"])
                
                # Add service name if not present
                if "service_name" not in log_entry:
                    log_entry["service_name"] = service_name
                
                matched_examples.append(log_entry)
        
        # Combine registry information
        combined_registry = {
            "service_name": service_name,
            "processed_files": {},
            "file_hashes": {},
            "last_updated": pendulum.now().isoformat()
        }
        
        # Merge processed files from all sources
        for source, logs in [("soap", soap_logs), ("rest", rest_logs), ("validation", validation_logs)]:
            if "registry" in logs and "processed_files" in logs["registry"]:
                if source in logs["registry"]["processed_files"]:
                    combined_registry["processed_files"][source] = logs["registry"]["processed_files"][source]
            
            # Merge file hashes
            if "registry" in logs and "file_hashes" in logs["registry"]:
                combined_registry["file_hashes"].update(logs["registry"]["file_hashes"])
        
        # Return the matched examples and registry
        return {
            "service_name": service_name,
            "examples": matched_examples,
            "example_count": len(matched_examples),
            "soap_log_count": soap_logs["pair_count"],
            "rest_log_count": rest_logs["pair_count"],
            "validation_log_count": validation_logs["entry_count"],
            "registry": combined_registry
        }

    @task(outlets=[OPERATIONAL_LOGS_DS])
    def store_operational_logs(matched_data: dict, output_path: str) -> dict:
        """Store the processed operational logs"""
        # Create a timestamp for versioning
        timestamp = pendulum.now().format('YYYYMMDD_HHmmss')
        service_name = matched_data.get("service_name", "unknown_service")
        
        # Create output directory
        output_dir = os.path.join(output_path, service_name)
        latest_dir = os.path.join(output_dir, f"v{timestamp}")
        os.makedirs(latest_dir, exist_ok=True)
        
        # Save the matched examples
        examples_file = os.path.join(latest_dir, "matched_examples.json")
        with open(examples_file, 'w') as f:
            json.dump(matched_data.get("examples", []), f, indent=2)
        
        # Save metadata
        metadata = {
            "service_name": service_name,
            "version": f"v{timestamp}",
            "example_count": len(matched_data.get("examples", [])),
            "soap_log_count": matched_data.get("soap_log_count", 0),
            "rest_log_count": matched_data.get("rest_log_count", 0),
            "validation_log_count": matched_data.get("validation_log_count", 0),
            "created_at": pendulum.now().isoformat()
        }
        
        metadata_file = os.path.join(latest_dir, "metadata.json")
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        # Update the catalog
        catalog_file = os.path.join(output_path, "operational_logs_catalog.json")
        logs_catalog = {"services": {}, "last_updated": ""}
        
        if os.path.exists(catalog_file):
            try:
                with open(catalog_file, 'r') as f:
                    logs_catalog = json.load(f)
            except json.JSONDecodeError:
                # If catalog is corrupted, start fresh
                logs_catalog = {"services": {}, "last_updated": ""}
        
        # Update catalog with this service's latest version
        if "services" not in logs_catalog:
            logs_catalog["services"] = {}
        
        logs_catalog["services"][service_name] = {
            "latest_version": f"v{timestamp}",
            "latest_path": latest_dir,
            "example_count": len(matched_data.get("examples", [])),
            "last_updated": pendulum.now().isoformat()
        }
        
        logs_catalog["last_updated"] = pendulum.now().isoformat()
        
        with open(catalog_file, 'w') as f:
            json.dump(logs_catalog, f, indent=2)
        
        # Also store in Airflow Variable for easy access by other DAGs
        try:
            Variable.set("operational_logs_catalog", json.dumps(logs_catalog))
        except Exception as e:
            print(f"Warning: Could not set Airflow Variable: {e}")
        
        return metadata

    # --- Helper Functions ---
    def load_processed_logs_registry(service_name: str, registry_path: str) -> dict:
        """Load the registry of processed log files"""
        registry = {
            "service_name": service_name,
            "processed_files": {},
            "file_hashes": {},
            "last_updated": pendulum.now().isoformat()
        }
        
        try:
            if os.path.exists(registry_path):
                with open(registry_path, 'r') as f:
                    loaded_registry = json.load(f)
                
                # Check if this service exists in the registry
                if service_name in loaded_registry:
                    registry = loaded_registry[service_name]
            
            return registry
        except Exception as e:
            print(f"Error loading registry: {e}")
            # Return empty registry on error
            return registry

    def save_processed_logs_registry(registry: dict, registry_path: str) -> None:
        """Save the updated registry of processed log files"""
        try:
            # Load existing registry if it exists
            full_registry = {}
            if os.path.exists(registry_path):
                with open(registry_path, 'r') as f:
                    full_registry = json.load(f)
            
            # Update with new registry
            service_name = registry["service_name"]
            full_registry[service_name] = registry
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(registry_path), exist_ok=True)
            
            # Save updated registry
            with open(registry_path, 'w') as f:
                json.dump(full_registry, f, indent=2)
                
            print(f"Registry saved to {registry_path}")
        except Exception as e:
            print(f"Error saving registry: {e}")

    def extract_date_from_filename(filename: str) -> Optional[pendulum.DateTime]:
        """Extract date from filename using common patterns"""
        # Try to find date patterns like YYYY-MM-DD, YYYYMMDD, etc.
        patterns = [
            r'(\d{4}-\d{2}-\d{2})',  # YYYY-MM-DD
            r'(\d{4}_\d{2}_\d{2})',  # YYYY_MM_DD
            r'(\d{8})',              # YYYYMMDD
        ]
        
        for pattern in patterns:
            match = re.search(pattern, os.path.basename(filename))
            if match:
                date_str = match.group(1)
                try:
                    if len(date_str) == 8 and date_str.isdigit():  # YYYYMMDD
                        return pendulum.from_format(date_str, 'YYYYMMDD')
                    elif '_' in date_str:  # YYYY_MM_DD
                        return pendulum.from_format(date_str, 'YYYY_MM_DD')
                    else:  # YYYY-MM-DD
                        return pendulum.from_format(date_str, 'YYYY-MM-DD')
                except ValueError:
                    continue
        
        return None

    def extract_soap_request_response_pairs(content: str) -> List[Dict]:
        """Extract SOAP request/response pairs from log content"""
        pairs = []
        
        # Simple pattern matching for SOAP envelopes - use raw strings for regex
        request_pattern = r'<soap(?:\w*):Envelope.*?</soap(?:\w*):Envelope>'
        response_pattern = r'<soap(?:\w*):Envelope.*?</soap(?:\w*):Envelope>'
        timestamp_pattern = r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})?)'
        
        # Find all requests
        requests = re.finditer(request_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for req_match in requests:
            req_xml = req_match.group(0)
            req_pos = req_match.end()
            
            # Look for timestamp before the request
            timestamp = None
            timestamp_match = re.search(timestamp_pattern, content[max(0, req_match.start() - 100):req_match.start()])
            if timestamp_match:
                timestamp = timestamp_match.group(1)
            
            # Look for operation name in the request
            operation = extract_soap_operation_name(req_xml)
            
            # Look for a response after this request
            resp_match = re.search(response_pattern, content[req_pos:req_pos + 10000], re.DOTALL | re.IGNORECASE)
            if resp_match:
                resp_xml = resp_match.group(0)
                
                # Check if this looks like a response (has Result or Response elements)
                if 'Response' in resp_xml or 'Result' in resp_xml or 'Fault' in resp_xml:
                    # Determine status (success/fault)
                    status = "success" if 'Fault' not in resp_xml else "fault"
                    
                    pairs.append({
                        "timestamp": timestamp,
                        "request": req_xml,
                        "response": resp_xml,
                        "operation": operation,
                        "status": status
                    })
        
        return pairs

    def extract_soap_operation_name(soap_xml: str) -> Optional[str]:
        """Extract operation name from SOAP XML"""
        # Look for the first element after the Body element - use raw strings
        body_pattern = r'<(?:\w+:)?Body[^>]*>(.*?)</(?:\w+:)?Body>'
        body_match = re.search(body_pattern, soap_xml, re.DOTALL | re.IGNORECASE)
        
        if body_match:
            body_content = body_match.group(1)
            # Find the first element in the body
            element_pattern = r'<(?!/)(\w+:)?(\w+)'
            element_match = re.search(element_pattern, body_content)
            if element_match:
                # Return the element name without namespace
                return element_match.group(2)
        
        return None

    def extract_rest_request_response_pairs(content: str) -> List[Dict]:
        """Extract REST request/response pairs from log content"""
        pairs = []
        
        # Look for common REST log patterns - use raw strings
        # This is a simplified example - real logs will vary widely
        
        # Pattern for HTTP request/response logs
        http_pattern = r'(GET|POST|PUT|DELETE|PATCH) ([^\s]+).*?HTTP/[\d.]+\s+(\d+)'
        
        # Find all HTTP requests
        matches = re.finditer(http_pattern, content)
        
        for match in matches:
            method = match.group(1)
            path = match.group(2)
            status = int(match.group(3))
            
            # Extract timestamp if available
            timestamp = None
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})?)', 
                                       content[max(0, match.start() - 100):match.start()])
            if timestamp_match:
                timestamp = timestamp_match.group(1)
            
            # Extract request headers and body
            req_headers = {}
            req_body = ""
            
            # Extract response headers and body
            resp_headers = {}
            resp_body = ""
            
            # Look for JSON in the content after the match
            json_pattern = r'({[\s\S]*?})'
            json_match = re.search(json_pattern, content[match.end():match.end() + 5000])
            if json_match:
                try:
                    # Try to parse as JSON
                    json_obj = json.loads(json_match.group(1))
                    # Assume this is the response body
                    resp_body = json.dumps(json_obj)
                except json.JSONDecodeError:
                    # Not valid JSON
                    pass
            
            pairs.append({
                "timestamp": timestamp,
                "method": method,
                "path": path,
                "request_headers": req_headers,
                "request_body": req_body,
                "response_status": status,
                "response_headers": resp_headers,
                "response_body": resp_body
            })
        
        return pairs

    def match_rest_path(actual_path: str, api_paths: List[str]) -> Tuple[Optional[str], Dict]:
        """Match an actual REST path to an API path template"""
        # Convert API path templates to regex patterns
        for api_path in api_paths:
            # Replace path parameters with regex patterns
            pattern = api_path
            param_names = re.findall(r'{([^}]+)}', api_path)
            for param in param_names:
                pattern = pattern.replace(f"{{{param}}}", r"([^/]+)")
            
            # Add start/end markers and escape special chars
            escaped_pattern = re.escape(pattern).replace(r'\\{[^}]+\}', r'([^/]+)')
            pattern = f"^{escaped_pattern}$"
            
            # Try to match
            match = re.match(pattern, actual_path)
            if match:
                # Extract parameter values
                params = {}
                for i, param_name in enumerate(param_names):
                    params[param_name] = match.group(i + 1)
                
                return api_path, params
        
        return None, {}

    def anonymize_xml(xml_content: str) -> str:
        """Anonymize sensitive data in XML content"""
        if not xml_content:
            return xml_content
        
        # Replace content of elements that might contain sensitive data
        sensitive_elements = [
            "password", "secret", "key", "token", "credential", "ssn", "creditcard",
            "account", "email", "phone", "address", "name", "user", "id"
        ]
        
        for element in sensitive_elements:
            # Pattern to match element content
            pattern = f"<({element}[^>]*)>(.*?)</\\1>"
            
            # Replace content with hash
            def hash_content(match):
                tag = match.group(1)
                content = match.group(2)
                if content and len(content) > 0:
                    hashed = hashlib.md5(content.encode()).hexdigest()[:8]
                    return f"<{tag}>[REDACTED-{hashed}]</{tag}>"
                return match.group(0)
            
            xml_content = re.sub(pattern, hash_content, xml_content, flags=re.IGNORECASE)
        
        return xml_content

    def anonymize_json(json_content: str) -> str:
        """Anonymize sensitive data in JSON content"""
        if not json_content:
            return json_content
        
        try:
            # Parse JSON
            data = json.loads(json_content)
            
            # Recursively anonymize
            def anonymize_obj(obj):
                if isinstance(obj, dict):
                    for key in obj:
                        # Check if key looks sensitive
                        lower_key = key.lower()
                        if any(sensitive in lower_key for sensitive in [
                            "password", "secret", "key", "token", "credential", "ssn", "credit",
                            "account", "email", "phone", "address", "name", "user", "id"
                        ]):
                            # Anonymize value
                            if isinstance(obj[key], str) and obj[key]:
                                hashed = hashlib.md5(obj[key].encode()).hexdigest()[:8]
                                obj[key] = f"[REDACTED-{hashed}]"
                        else:
                            # Recurse
                            obj[key] = anonymize_obj(obj[key])
                elif isinstance(obj, list):
                    obj = [anonymize_obj(item) for item in obj]
                return obj
            
            # Anonymize the data
            anonymized_data = anonymize_obj(data)
            
            # Convert back to JSON
            return json.dumps(anonymized_data)
        except (json.JSONDecodeError, TypeError):
            # If not valid JSON, return as is
            return json_content

    def anonymize_headers(headers: Dict) -> Dict:
        """Anonymize sensitive headers"""
        if not headers:
            return headers
        
        sensitive_headers = [
            "authorization", "cookie", "set-cookie", "x-api-key", "api-key",
            "token", "secret", "password", "credential"
        ]
        
        anonymized = headers.copy()
        for header, value in headers.items():
            if header.lower() in sensitive_headers and value:
                hashed = hashlib.md5(str(value).encode()).hexdigest()[:8]
                anonymized[header] = f"[REDACTED-{hashed}]"
        
        return anonymized

    def truncate_xml(xml_content: str, max_length: int) -> str:
        """Truncate XML content to a maximum length"""
        if not xml_content or len(xml_content) <= max_length:
            return xml_content
        
        # Try to truncate at a tag boundary
        truncated = xml_content[:max_length]
        last_close = truncated.rfind('>')
        if last_close > 0:
            truncated = truncated[:last_close + 1]
        
        return truncated + "..."

    def truncate_json(json_content: str, max_length: int) -> str:
        """Truncate JSON content to a maximum length"""
        if not json_content or len(json_content) <= max_length:
            return json_content
        
        try:
            # Parse JSON
            data = json.loads(json_content)
            
            # Convert back with limited length
            truncated = json.dumps(data)[:max_length]
            
            # Try to find a valid truncation point
            last_brace = max(truncated.rfind('}'), truncated.rfind(']'))
            if last_brace > 0:
                truncated = truncated[:last_brace + 1]
                
                # Try to make it valid JSON again
                try:
                    json.loads(truncated)
                    return truncated
                except json.JSONDecodeError:
                    pass
            
            return truncated + "..."
        except (json.JSONDecodeError, TypeError):
            # If not valid JSON, truncate directly
            return json_content[:max_length] + "..."

    def parse_validation_json_log(content: str, log_file: str, file_date: pendulum.DateTime) -> List[Dict]:
        """Parse a JSON validation log file"""
        try:
            # Try to parse the file as JSON
            data = json.loads(content)
            
            # Handle different JSON validation log formats
            results = []
            
            # If it's an array, process each entry
            if isinstance(data, list):
                for entry in data:
                    if "soap_request" in entry or "rest_request_body" in entry:
                        # This looks like a validation log entry
                        entry["source"] = entry.get("source", "validation_log")
                        entry["source_file"] = log_file
                        
                        # Ensure timestamp exists
                        if "timestamp" not in entry:
                            entry["timestamp"] = file_date.isoformat()
                            
                        results.append(entry)
            # If it's an object with a 'logs' or 'entries' field
            elif isinstance(data, dict):
                entries = data.get("logs", data.get("entries", []))
                if isinstance(entries, list):
                    for entry in entries:
                        if "soap_request" in entry or "rest_request_body" in entry:
                            # This looks like a validation log entry
                            entry["source"] = entry.get("source", "validation_log")
                            entry["source_file"] = log_file
                            
                            # Ensure timestamp exists
                            if "timestamp" not in entry:
                                entry["timestamp"] = file_date.isoformat()
                                
                            results.append(entry)
                # If it's a single validation entry
                elif "soap_request" in data or "rest_request_body" in data:
                    data["source"] = data.get("source", "validation_log")
                    data["source_file"] = log_file
                    
                    # Ensure timestamp exists
                    if "timestamp" not in data:
                        data["timestamp"] = file_date.isoformat()
                        
                    results.append(data)
            
            return results
        except json.JSONDecodeError:
            print(f"File {log_file} is not valid JSON")
            return []

    def parse_validation_xml_log(content: str, log_file: str, file_date: pendulum.DateTime) -> List[Dict]:
        """Parse an XML validation log file"""
        # This is a simplified implementation - real XML parsing would use lxml or similar
        results = []
        
        # Look for validation entries in XML format
        # Example pattern: <ValidationEntry>...</ValidationEntry>
        entry_pattern = r'<ValidationEntry>(.*?)</ValidationEntry>'
        entries = re.finditer(entry_pattern, content, re.DOTALL)
        
        for entry_match in entries:
            entry_xml = entry_match.group(1)
            
            # Extract SOAP request/response if present
            soap_req = extract_xml_element(entry_xml, "SoapRequest")
            soap_resp = extract_xml_element(entry_xml, "SoapResponse")
            
            # Extract REST request/response if present
            rest_method = extract_xml_element(entry_xml, "RestMethod")
            rest_path = extract_xml_element(entry_xml, "RestPath")
            rest_req_body = extract_xml_element(entry_xml, "RestRequestBody")
            rest_resp_body = extract_xml_element(entry_xml, "RestResponseBody")
            
            # Extract mapping info if present
            mapping_type = extract_xml_element(entry_xml, "MappingType")
            
            # Create validation entry
            validation_entry = {
                "source": "validation_" + (mapping_type.lower() if mapping_type else "log"),
                "source_file": log_file,
                "timestamp": extract_xml_element(entry_xml, "Timestamp") or file_date.isoformat()
            }
            
            # Add SOAP data if present
            if soap_req:
                validation_entry["soap_request"] = soap_req
            if soap_resp:
                validation_entry["soap_response"] = soap_resp
                
            # Add REST data if present
            if rest_method:
                validation_entry["rest_method"] = rest_method
            if rest_path:
                validation_entry["rest_path"] = rest_path
            if rest_req_body:
                validation_entry["rest_request_body"] = rest_req_body
            if rest_resp_body:
                validation_entry["rest_response_body"] = rest_resp_body
                
            # Add to results if it has useful data
            if soap_req or rest_req_body:
                results.append(validation_entry)
        
        return results

    def extract_xml_element(xml_content: str, element_name: str) -> Optional[str]:
        """Extract the content of an XML element"""
        pattern = f"<{element_name}>(.*?)</{element_name}>"
        match = re.search(pattern, xml_content, re.DOTALL)
        if match:
            return match.group(1).strip()
        return None

    def parse_validation_text_log(content: str, log_file: str, file_date: pendulum.DateTime) -> List[Dict]:
        """Parse a text validation log file"""
        results = []
        
        # Look for validation entries in text format
        # This is highly dependent on your log format, but here's a simple example
        
        # Example: Look for sections separated by a specific marker
        entry_sections = re.split(r'={20,}', content)
        
        for section in entry_sections:
            if not section.strip():
                continue
                
            # Try to identify if this is a validation entry
            is_soap = "SOAP Request" in section or "<soap:Envelope" in section
            is_rest = "REST Request" in section or "HTTP/1.1" in section
            
            if not (is_soap or is_rest):
                continue
                
            # Extract timestamp if present
            timestamp = None
            timestamp_match = re.search(r'Timestamp: (\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})?)', section)
            if timestamp_match:
                timestamp = timestamp_match.group(1)
            else:
                # Try another common format
                timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(?:\.\d+)?)', section)
                if timestamp_match:
                    # Convert to ISO format
                    dt = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                    timestamp = dt.isoformat()
                else:
                    timestamp = file_date.isoformat()
            
            # Create validation entry
            validation_entry = {
                "source": "validation_log",
                "source_file": log_file,
                "timestamp": timestamp
            }
            
            # Extract SOAP data if present
            if is_soap:
                soap_req_match = re.search(r'SOAP Request:?\s*\n(.*?)<soap:Envelope.*?</soap:Envelope>', 
                                          section, re.DOTALL | re.IGNORECASE)
                if soap_req_match:
                    validation_entry["soap_request"] = soap_req_match.group(1)
                
                soap_resp_match = re.search(r'SOAP Response:?\s*\n(.*?)<soap:Envelope.*?</soap:Envelope>', 
                                           section, re.DOTALL | re.IGNORECASE)
                if soap_resp_match:
                    validation_entry["soap_response"] = soap_resp_match.group(1)
            
            # Extract REST data if present
            if is_rest:
                # Extract method and path
                rest_req_match = re.search(r'(GET|POST|PUT|DELETE|PATCH) ([^\s]+) HTTP', section)
                if rest_req_match:
                    validation_entry["rest_method"] = rest_req_match.group(1)
                    validation_entry["rest_path"] = rest_req_match.group(2)
                
                # Extract request body if present
                rest_req_body_match = re.search(r'REST Request Body:?\s*\n(.*?)(?:\n\n|\nREST Response)', 
                                               section, re.DOTALL)
                if rest_req_body_match:
                    validation_entry["rest_request_body"] = rest_req_body_match.group(1).strip()
                
                # Extract response body if present
                rest_resp_body_match = re.search(r'REST Response Body:?\s*\n(.*?)(?:\n\n|$)', 
                                                section, re.DOTALL)
                if rest_resp_body_match:
                    validation_entry["rest_response_body"] = rest_resp_body_match.group(1).strip()
            
            # Add to results if it has useful data
            if "soap_request" in validation_entry or "rest_method" in validation_entry:
                results.append(validation_entry)
        
        return results

    # --- Task Execution Flow ---
    # Parse parameters
    service_name = "{{ params.service_name }}"
    soap_log_path = "{{ params.soap_log_path }}"
    rest_log_path = "{{ params.rest_log_path }}"
    validation_log_path = "{{ params.validation_log_path }}"
    log_date_range = int("{{ params.log_date_range }}")
    output_path = "{{ params.output_path }}"
    processed_logs_registry = "{{ params.processed_logs_registry }}"
    sample_rate = float("{{ params.sample_rate }}")
    anonymize_data = "{{ params.anonymize_data }}" == "True"
    force_reprocess = "{{ params.force_reprocess }}" == "True"

    # Check if service exists
    service_metadata = validate_service_exists(service_name)
    
    # Load the registry of processed logs
    registry = load_processed_logs_registry(service_name, processed_logs_registry)

    # Collect logs
    soap_logs = collect_soap_logs(soap_log_path, service_name, log_date_range, sample_rate, registry, force_reprocess)
    rest_logs = collect_rest_logs(rest_log_path, service_name, log_date_range, sample_rate, registry, force_reprocess)
    validation_logs = collect_validation_logs(validation_log_path, service_name, log_date_range, sample_rate, registry, force_reprocess)

    # Match logs with definitions
    matched_data = match_logs_with_definitions(soap_logs, rest_logs, validation_logs, service_name, anonymize_data)

    # Store the processed logs
    metadata = store_operational_logs(matched_data, output_path)

    # Save the updated registry
    save_processed_logs_registry(matched_data["registry"], processed_logs_registry)

    # Optimize file reading with a context manager
    def read_file_with_timeout(file_path: str, timeout: int = 30, chunk_size: int = 8192) -> str:
        """Read a file with timeout to prevent hanging on large files"""
        import signal
        
        class TimeoutError(Exception):
            pass
            
        def timeout_handler(signum, frame):
            raise TimeoutError(f"Reading file {file_path} timed out after {timeout} seconds")
            
        content = []
        try:
            # Set timeout
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(timeout)
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    content.append(chunk)
            
            # Cancel timeout
            signal.alarm(0)
            
            return ''.join(content)
        except TimeoutError as e:
            print(f"Warning: {e}")
            return ""
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return ""

    # Add caching for expensive operations
    def cached_hash_file(file_path: str, cache: Dict[str, str] = {}) -> str:
        """Hash a file with caching based on modification time"""
        try:
            # Get file modification time
            mtime = os.path.getmtime(file_path)
            
            # Check if we have a cached hash for this file and mtime
            cache_key = f"{file_path}:{mtime}"
            if cache_key in cache:
                return cache[cache_key]
            
            # Calculate hash
            with open(file_path, 'rb') as f:
                file_hash = hashlib.md5(f.read()).hexdigest()
            
            # Cache the result
            cache[cache_key] = file_hash
            
            return file_hash
        except Exception as e:
            print(f"Error hashing file {file_path}: {e}")
            return f"error-{pendulum.now().timestamp()}"  # Return a unique error hash
