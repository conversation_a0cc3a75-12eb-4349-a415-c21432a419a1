"""
DAG for operational service mapping pipeline.

This DAG performs real-time service mapping using trained ML models and
heuristic engines. It processes incoming service requests, applies mappings,
and handles operational concerns like monitoring and error handling.

Author: Airflow LLM Service Mapper Team
Version: 1.0.0
"""

from __future__ import annotations

import json
import logging
import pendulum
from typing import Dict, List, Any, Optional

from airflow.decorators import dag, task
from airflow.datasets import Dataset
from airflow.exceptions import AirflowException
from airflow.models.param import Param
from airflow.utils.context import Context

# Import shared modules
import sys
import os

# Add shared directory to Python path
# In Docker container, shared is mounted at /opt/airflow/shared
shared_path = '/opt/airflow/shared'
if os.path.exists(shared_path) and shared_path not in sys.path:
    sys.path.append(shared_path)
else:
    # Fallback for local development (relative path)
    shared_path = os.path.join(os.path.dirname(__file__), '..', '..', 'shared')
    if os.path.exists(shared_path) and shared_path not in sys.path:
        sys.path.append(shared_path)

from shared.common import constants, validation_utils
from shared.common.definition_store import DefinitionStore
from shared.common.model_management import load_latest_model
from shared.common.heuristic_engine import HeuristicEngine
from shared.common.llm_utils import LLMClient

# Configure logging
logger = logging.getLogger(__name__)

# Define datasets for lineage tracking
TRAINED_MODEL_DS = Dataset(constants.TRAINED_MODEL_DATASET_URI)
OPERATIONAL_MAPPING_DS = Dataset(constants.OPERATIONAL_MAPPING_DATASET_URI)

@dag(
    dag_id="04_operational_mapping_pipeline",
    description="Operational service mapping pipeline using trained models",
    schedule=None,  # Triggered by external requests or events
    start_date=pendulum.datetime(2024, 1, 1, tz="UTC"),
    catchup=False,
    max_active_runs=10,  # Allow multiple concurrent mapping requests
    max_active_tasks=20,
    default_args={
        "owner": "llm-mapper-team",
        "depends_on_past": False,
        "email_on_failure": True,
        "email_on_retry": False,
        "retries": 3,
        "retry_delay": pendulum.duration(minutes=2),
    },
    tags=["llm-mapper", "operational", "mapping", "production"],
    params={
        "source_service": Param(
            "example_service",
            type="string",
            description="Source service name to map from.",
            pattern=r"^[a-zA-Z0-9_-]+$"
        ),
        "target_service": Param(
            "target_api",
            type="string",
            description="Target service name to map to.",
            pattern=r"^[a-zA-Z0-9_-]+$"
        ),
        "mapping_request": Param(
            {},
            type="object",
            description="Service mapping request payload."
        ),
        "use_ml_model": Param(
            True,
            type="boolean",
            description="Whether to use ML model for mapping."
        ),
        "use_heuristics": Param(
            True,
            type="boolean",
            description="Whether to use heuristic engine as fallback."
        ),
        "confidence_threshold": Param(
            0.8,
            type="number",
            description="Minimum confidence threshold for ML predictions."
        ),
        "output_path": Param(
            "data/operational_mappings",
            type="string",
            description="Path to store mapping results."
        ),
    },
    doc_md="""
    ## Operational Service Mapping Pipeline DAG

    This DAG performs real-time service mapping using trained ML models and
    heuristic engines. It's designed for production use with high availability
    and performance requirements.

    ### Workflow
    1. **Validate Request**: Ensures mapping request is valid
    2. **Load Models**: Retrieves latest trained models and configurations
    3. **Apply ML Mapping**: Uses trained models for service mapping
    4. **Apply Heuristics**: Falls back to heuristic engine if needed
    5. **Validate Results**: Ensures mapping results are valid
    6. **Store Results**: Saves mapping results and metadata
    7. **Monitor Performance**: Tracks mapping quality and performance

    ### Inputs
    - **source_service**: Source service identifier
    - **target_service**: Target service identifier
    - **mapping_request**: Service request payload to map
    - **use_ml_model**: Whether to use ML model
    - **use_heuristics**: Whether to use heuristic fallback
    - **confidence_threshold**: Minimum ML confidence threshold
    - **output_path**: Directory to store results

    ### Outputs
    - Mapped service request/response
    - Mapping confidence scores
    - Performance metrics
    - Error logs and diagnostics

    ### Dependencies
    - Requires: 03_train_ml_model_dag (for trained models)
    - Triggers: 05_monitoring_and_retraining_trigger_dag (for quality monitoring)

    ### Performance Considerations
    - Optimized for low latency
    - Supports concurrent mapping requests
    - Includes fallback mechanisms
    - Comprehensive error handling
    """,
)
def operational_mapping_pipeline_dag():
    """
    Main DAG function for operational service mapping pipeline.

    Returns:
        DAG: Configured Airflow DAG instance
    """

    @task
    def validate_mapping_request(
        source_service: str,
        target_service: str,
        mapping_request: Dict[str, Any],
        **context: Context
    ) -> Dict[str, Any]:
        """
        Validate the incoming mapping request.

        Args:
            source_service: Source service identifier
            target_service: Target service identifier
            mapping_request: Request payload to validate
            **context: Airflow context

        Returns:
            Dict containing validated request parameters

        Raises:
            AirflowException: If validation fails
        """
        logger.info(f"Validating mapping request: {source_service} -> {target_service}")

        # Validate service names
        for service_name in [source_service, target_service]:
            if not service_name or not service_name.strip():
                raise AirflowException(f"Service name cannot be empty: {service_name}")

            # Check if service exists in catalog
            if not DefinitionStore.service_exists(service_name):
                available_services = DefinitionStore.list_available_services()
                raise AirflowException(
                    f"Service '{service_name}' not found in catalog. "
                    f"Available services: {available_services}"
                )

        # Validate mapping request structure
        if not isinstance(mapping_request, dict):
            raise AirflowException("mapping_request must be a dictionary")

        if not mapping_request:
            raise AirflowException("mapping_request cannot be empty")

        # Basic request structure validation
        required_fields = ["operation", "payload"]
        for field in required_fields:
            if field not in mapping_request:
                raise AirflowException(f"Required field '{field}' missing from mapping_request")

        logger.info("Mapping request validation successful")
        return {
            "source_service": source_service,
            "target_service": target_service,
            "mapping_request": mapping_request,
            "validation_timestamp": pendulum.now().isoformat()
        }

# Instantiate the DAG
operational_mapping_pipeline_dag_instance = operational_mapping_pipeline_dag()