"""
DAG for generating training data from parsed service definitions.

This DAG retrieves previously parsed service definitions from the catalog
and generates structured training data for ML models. It processes WSDL,
OpenAPI, and other service definition formats to create training examples.

Author: Airflow LLM Service Mapper Team
Version: 1.0.0
"""

from __future__ import annotations

import json
import logging
import os
import pendulum
import re
from typing import Dict, List, Any, Optional

from airflow.decorators import dag, task
from airflow.datasets import Dataset
from airflow.exceptions import AirflowException
from airflow.models.param import Param
from airflow.utils.context import Context

# Import shared modules
import sys

# Add shared directory to Python path
# In Docker container, shared is mounted at /opt/airflow/shared
shared_path = '/opt/airflow/shared'
if os.path.exists(shared_path) and shared_path not in sys.path:
    sys.path.append(shared_path)
else:
    # Fallback for local development (relative path)
    shared_path = os.path.join(os.path.dirname(__file__), '..', '..', 'shared')
    if os.path.exists(shared_path) and shared_path not in sys.path:
        sys.path.append(shared_path)

from shared.common import constants, data_parsers, validation_utils
from shared.common.definition_store import DefinitionStore

# Configure logging
logger = logging.getLogger(__name__)

# Define datasets for lineage tracking
PARSED_DEFINITIONS_DS = Dataset(constants.PARSED_DEFINITIONS_DATASET_URI)
TRAINING_DATA_DS = Dataset(constants.TRAINING_DATA_DATASET_URI)

@dag(
    dag_id="02_generate_training_data",
    description="Generate training data from parsed service definitions",
    schedule=[PARSED_DEFINITIONS_DS],  # Triggered when new definitions are available
    start_date=pendulum.datetime(2024, 1, 1, tz="UTC"),
    catchup=False,
    max_active_runs=5,  # Allow multiple services to be processed concurrently
    max_active_tasks=15,
    default_args={
        "owner": "llm-mapper-team",
        "depends_on_past": False,
        "email_on_failure": False,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": pendulum.duration(minutes=3),
    },
    tags=["llm-mapper", "training-data", "ml-prep", "data-pipeline"],
    params={
        "service_name": Param(
            "example_service",
            type="string",
            description="Name of the service to process (must be in catalog).",
            pattern=r"^[a-zA-Z0-9_-]+$"
        ),
        "include_wsdl": Param(
            True,
            type="boolean",
            description="Whether to include WSDL data in training."
        ),
        "include_openapi": Param(
            True,
            type="boolean",
            description="Whether to include OpenAPI data in training."
        ),
        "output_path": Param(
            "data/training_data",
            type="string",
            description="Path to store generated training data."
        ),
        "max_examples_per_operation": Param(
            100,
            type="integer",
            description="Maximum number of training examples per operation."
        ),
        "include_java_code": Param(
            False,
            type="boolean",
            description="Whether to generate Java code from definitions."
        ),
        "java_repo_url": Param(
            "",
            type="string",
            description="URL of the Java repository to checkout (if include_java_code is True)."
        ),
        "java_branch": Param(
            "main",
            type="string",
            description="Branch of the Java repository to checkout."
        ),
        "java_build_command": Param(
            "mvn clean install",
            type="string",
            description="Maven build command to execute."
        )
    },
    doc_md="""
    ## Generate Training Data DAG

    This DAG retrieves previously parsed service definitions from the catalog
    and generates structured training data for ML models. It processes WSDL,
    OpenAPI, and optionally Java code to create comprehensive training examples.

    ### Workflow
    1. **Validate Service**: Ensures the service exists in the catalog
    2. **Load Definitions**: Retrieves parsed service definitions
    3. **Generate Examples**: Creates training examples from definitions
    4. **Java Code Generation** (optional): Generates Java classes from definitions
    5. **Store Training Data**: Saves structured training data for ML models

    ### Inputs
    - **service_name**: Service identifier (must exist in catalog)
    - **include_wsdl**: Whether to process WSDL definitions
    - **include_openapi**: Whether to process OpenAPI definitions
    - **output_path**: Directory to store training data
    - **max_examples_per_operation**: Limit on examples per operation
    - **include_java_code**: Whether to generate Java code
    - **java_repo_url**: Repository URL for Java code generation
    - **java_branch**: Git branch to checkout
    - **java_build_command**: Maven command to execute

    ### Outputs
    - Structured training examples in JSON format
    - Training data metadata and statistics
    - Generated Java code (if enabled)
    - Dataset trigger for ML training DAGs

    ### Dependencies
    - Requires: 01_ingest_and_parse_definitions_dag
    - Triggers: 03_train_ml_model_dag

    ### Data Sources
    - WSDL operations and message definitions
    - OpenAPI paths, operations, and schemas
    - Generated Java classes and interfaces
    - Service metadata from catalog
    """,
)
def generate_training_data_dag():

    @task
    def validate_service_exists(service_name: str) -> dict:
        """Check if the service exists in the catalog and return its metadata"""
        metadata = DefinitionStore.get_service_metadata(service_name)
        if not metadata:
            available_services = DefinitionStore.list_available_services()
            raise ValueError(f"Service '{service_name}' not found in catalog. Available services: {available_services}")
        return metadata

    @task
    def load_service_definitions(service_name: str, include_wsdl: bool, include_openapi: bool) -> dict:
        """Load the parsed definitions for the service from storage"""
        definitions = {}
        
        if include_wsdl:
            wsdl_data = DefinitionStore.load_parsed_definition(service_name, "wsdl")
            if "wsdl" in wsdl_data:
                definitions["wsdl"] = wsdl_data["wsdl"]
        
        if include_openapi:
            openapi_data = DefinitionStore.load_parsed_definition(service_name, "openapi")
            if "openapi" in openapi_data:
                definitions["openapi"] = openapi_data["openapi"]
        
        if not definitions:
            raise ValueError(f"No definitions loaded for service '{service_name}'")
        
        return definitions

    @task
    def generate_training_examples(definitions: dict, service_name: str) -> list:
        """Generate training examples from the definitions"""
        training_examples = []
        
        # Process WSDL data if available
        if "wsdl" in definitions:
            wsdl_data = definitions["wsdl"]
            
            # Generate examples from operations
            for operation in wsdl_data.get("operations", []):
                example = {
                    "source": "wsdl",
                    "service_name": service_name,
                    "operation_name": operation.get("name"),
                    "input_message": operation.get("input_message"),
                    "output_message": operation.get("output_message"),
                    "features": {
                        "operation_type": "unknown",  # To be determined by ML
                        "complexity": "unknown",      # To be determined by ML
                        "data_types": []              # To be filled based on messages
                    }
                }
                training_examples.append(example)

            # Generate examples from XSD types/schemas
            for type_def in wsdl_data.get("schemas", {}).get("types", []):
                type_example = {
                    "source": "wsdl_schema",
                    "service_name": service_name,
                    "type_name": type_def.get("name"),
                    "type_namespace": type_def.get("namespace"),
                    "type_structure": type_def.get("structure"),
                    "features": {
                        "complexity": "unknown",  # To be determined by ML
                        "is_complex_type": type_def.get("is_complex_type", False),
                        "has_attributes": len(type_def.get("attributes", [])) > 0,
                        "element_count": len(type_def.get("elements", [])),
                        "nesting_level": type_def.get("nesting_level", 0)
                    }
                }
                training_examples.append(type_example)

        # Process XSD data if available separately
        if "xsd" in definitions:
            xsd_data = definitions["xsd"]

            # Generate examples from XSD types
            for type_def in xsd_data.get("parsed_xsd", {}).get("types", []):
                type_example = {
                    "source": "xsd",
                    "service_name": service_name,
                    "type_name": type_def.get("name"),
                    "type_namespace": type_def.get("namespace"),
                    "type_structure": type_def.get("structure"),
                    "features": {
                        "complexity": "unknown",  # To be determined by ML
                        "is_complex_type": type_def.get("is_complex_type", False),
                        "has_attributes": len(type_def.get("attributes", [])) > 0,
                        "element_count": len(type_def.get("elements", [])),
                        "nesting_level": type_def.get("nesting_level", 0)
                    }
                }
                training_examples.append(type_example)

        # Process OpenAPI data if available
        if "openapi" in definitions:
            openapi_data = definitions["openapi"]
            
            # Extract from full_spec or extracted structure
            spec = openapi_data.get("full_spec", openapi_data.get("extracted", {}))
            
            # Generate examples from paths/operations
            for path, methods in spec.get("paths", {}).items():
                for method, operation in methods.items():
                    if method in ['get', 'post', 'put', 'delete', 'patch']:
                        example = {
                            "source": "openapi",
                            "service_name": service_name,
                            "path": path,
                            "method": method,
                            "operation_id": operation.get("operationId"),
                            "summary": operation.get("summary"),
                            "features": {
                                "operation_type": method,
                                "complexity": "unknown",  # To be determined by ML
                                "has_request_body": bool(operation.get("requestBody")),
                                "response_codes": list(operation.get("responses", {}).keys())
                            }
                        }
                        training_examples.append(example)

            # Generate examples from schema objects
            schemas = spec.get("components", {}).get("schemas", {})
            for schema_name, schema_def in schemas.items():
                schema_example = {
                    "source": "openapi_schema",
                    "service_name": service_name,
                    "schema_name": schema_name,
                    "schema_type": schema_def.get("type", "object"),
                    "schema_structure": schema_def,
                    "features": {
                        "complexity": "unknown",  # To be determined by ML
                        "property_count": len(schema_def.get("properties", {})),
                        "required_props": len(schema_def.get("required", [])),
                        "has_array": any(prop.get("type") == "array" for prop in schema_def.get("properties", {}).values()),
                        "has_refs": "$ref" in json.dumps(schema_def),
                        "has_enums": any("enum" in prop for prop in schema_def.get("properties", {}).values())
                    }
                }
                training_examples.append(schema_example)

        # Generate mapping examples (WSDL to OpenAPI)
        if "wsdl" in definitions and "openapi" in definitions:
            # Try to find potential mappings between WSDL operations and OpenAPI paths
            wsdl_data = definitions["wsdl"]
            openapi_data = definitions["openapi"]
            spec = openapi_data.get("full_spec", openapi_data.get("extracted", {}))

            for wsdl_op in wsdl_data.get("operations", []):
                wsdl_op_name = wsdl_op.get("name", "").lower()

                # Look for potential matches in OpenAPI
                for path, methods in spec.get("paths", {}).items():
                    for method, operation in methods.items():
                        if method in ['get', 'post', 'put', 'delete', 'patch']:
                            op_id = operation.get("operationId", "").lower()
                            summary = operation.get("summary", "").lower()

                            # Check if there's a potential match
                            if (wsdl_op_name in op_id or
                                wsdl_op_name in summary or
                                wsdl_op_name in path.lower()):

                                mapping_example = {
                                    "source": "mapping",
                                    "service_name": service_name,
                                    "wsdl_operation": wsdl_op.get("name"),
                                    "openapi_path": path,
                                    "openapi_method": method,
                                    "openapi_operation_id": operation.get("operationId"),
                                    "features": {
                                        "mapping_confidence": "medium",  # Placeholder
                                        "name_similarity": "high" if wsdl_op_name in op_id else "medium",
                                        "parameter_count_match": "unknown"  # Would need deeper analysis
                                    }
                                }
                                training_examples.append(mapping_example)

        return training_examples

    @task(outlets=[TRAINING_DATA_DS])
    def store_training_data(training_examples: list, service_name: str, output_path: str) -> dict:
        """Store the generated training data using the standardized format"""
        from shared.common.training_data_store import TrainingDataStore
        
        # Store the examples using the new standardized format
        metadata = TrainingDataStore.store_training_examples(
            examples=training_examples,
            service_name=service_name,
            data_type="definitions",  # This DAG processes service definitions
            base_path=output_path
        )
        
        return metadata

    @task
    def checkout_and_build_java_repo(service_name: str, repo_url: str, branch: str = "main", 
                                    build_command: str = "mvn clean install") -> dict:
        """
        Checkout a Maven Java repository and execute a build step to generate Java classes.
        
        Args:
            service_name: Name of the service
            repo_url: URL of the Git repository
            branch: Branch to checkout (default: main)
            build_command: Maven build command to execute (default: mvn clean install)
            
        Returns:
            Dictionary with information about the checkout and build process
        """
        import subprocess
        import tempfile
        import glob
        from datetime import datetime
        
        # Create a temporary directory for the checkout
        temp_dir = tempfile.mkdtemp(prefix=f"java-repo-{service_name}-")
        print(f"Created temporary directory: {temp_dir}")
        
        try:
            # Clone the repository
            print(f"Cloning repository {repo_url} (branch: {branch})...")
            clone_cmd = ["git", "clone", "--branch", branch, "--single-branch", repo_url, temp_dir]
            clone_result = subprocess.run(clone_cmd, capture_output=True, text=True, check=True)
            
            # Execute the build command
            print(f"Executing build command: {build_command}")
            build_cmd = build_command.split()
            build_result = subprocess.run(build_cmd, cwd=temp_dir, capture_output=True, text=True)
            
            if build_result.returncode != 0:
                print(f"Build failed with exit code {build_result.returncode}")
                print(f"Build output: {build_result.stdout}")
                print(f"Build error: {build_result.stderr}")
                raise Exception(f"Build command failed with exit code {build_result.returncode}")
            
            # Find generated Java classes (typically in target/generated-sources)
            generated_sources_dir = os.path.join(temp_dir, "target", "generated-sources")
            
            # Look for Java files in common generated-sources directories
            java_files = []
            for pattern in ["**/generated-sources/**/*.java", "**/generated/**/*.java"]:
                java_files.extend(glob.glob(os.path.join(temp_dir, pattern), recursive=True))
            
            print(f"Found {len(java_files)} generated Java files")
            
            # Extract package and class information from Java files
            java_classes = []
            for java_file in java_files:
                with open(java_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # Extract package name
                    package_match = re.search(r'package\s+([\w.]+);', content)
                    package = package_match.group(1) if package_match else "unknown"
                    
                    # Extract class name
                    class_match = re.search(r'(public|private)?\s+(class|interface|enum)\s+(\w+)', content)
                    class_name = class_match.group(3) if class_match else os.path.basename(java_file).replace(".java", "")
                    
                    # Extract method signatures
                    method_matches = re.findall(r'(public|private|protected)?\s+[\w<>\[\]]+\s+(\w+)\s*\((.*?)\)', content)
                    methods = []
                    
                    for access, name, params in method_matches:
                        methods.append({
                            "name": name,
                            "parameters": [p.strip() for p in params.split(',') if p.strip()],
                            "access": access if access else "default"
                        })
                    
                    java_classes.append({
                        "file_path": os.path.relpath(java_file, temp_dir),
                        "package": package,
                        "class_name": class_name,
                        "full_name": f"{package}.{class_name}",
                        "methods": methods,
                        "content": content
                    })
            
            return {
                "service_name": service_name,
                "repo_url": repo_url,
                "branch": branch,
                "checkout_dir": temp_dir,
                "build_command": build_command,
                "build_success": True,
                "java_file_count": len(java_files),
                "java_classes": java_classes,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"Error during checkout or build: {str(e)}")
            return {
                "service_name": service_name,
                "repo_url": repo_url,
                "branch": branch,
                "checkout_dir": temp_dir,
                "build_command": build_command,
                "build_success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    @task
    def generate_training_data_from_java_classes(build_result: dict, service_name: str) -> list:
        """
        Generate training examples from Java classes generated during the build.
        
        Args:
            build_result: Result from checkout_and_build_java_repo task
            service_name: Name of the service
            
        Returns:
            List of training examples
        """
        if not build_result.get("build_success", False):
            print(f"Build was not successful, cannot generate training data")
            return []
        
        java_classes = build_result.get("java_classes", [])
        print(f"Generating training data from {len(java_classes)} Java classes")
        
        training_examples = []
        
        for java_class in java_classes:
            # Create a training example for each Java class
            class_example = {
                "source": "java_class",
                "service_name": service_name,
                "class_name": java_class["class_name"],
                "package": java_class["package"],
                "full_name": java_class["full_name"],
                "file_path": java_class["file_path"],
                "method_count": len(java_class["methods"]),
                "features": {
                    "is_generated": True,
                    "package_depth": len(java_class["package"].split('.')),
                    "method_count": len(java_class["methods"]),
                    "has_getters_setters": any(m["name"].startswith("get") or m["name"].startswith("set") 
                                              for m in java_class["methods"]),
                    "class_name_tokens": re.findall(r'[A-Z][a-z]*', java_class["class_name"])
                }
            }
            training_examples.append(class_example)
            
            # Create examples for methods that might represent API operations
            for method in java_class["methods"]:
                # Skip simple getters and setters
                if (method["name"].startswith("get") or method["name"].startswith("set")) and len(method["parameters"]) <= 1:
                    continue
                    
                method_example = {
                    "source": "java_method",
                    "service_name": service_name,
                    "class_name": java_class["class_name"],
                    "package": java_class["package"],
                    "method_name": method["name"],
                    "parameter_count": len(method["parameters"]),
                    "features": {
                        "is_generated": True,
                        "is_public": method["access"] == "public",
                        "parameter_count": len(method["parameters"]),
                        "method_name_tokens": re.findall(r'[A-Z][a-z]*', method["name"]) if method["name"][0].isupper() 
                                             else [method["name"]] + re.findall(r'[A-Z][a-z]*', method["name"])
                    }
                }
                training_examples.append(method_example)
        
        # Try to identify potential mappings between Java methods and API operations
        # This is a simplified approach - in a real implementation, you'd want more sophisticated matching
        wsdl_operations = [ex for ex in training_examples if ex["source"] == "wsdl" and "element_type" in ex and ex["element_type"] == "operation"]
        java_methods = [ex for ex in training_examples if ex["source"] == "java_method"]
        
        for wsdl_op in wsdl_operations:
            op_name = wsdl_op.get("element_name", "").lower()
            
            for java_method in java_methods:
                method_name = java_method.get("method_name", "").lower()
                
                # Simple name-based matching
                if op_name in method_name or method_name in op_name:
                    mapping_example = {
                        "source": "wsdl_java_mapping",
                        "service_name": service_name,
                        "wsdl_operation": wsdl_op.get("element_name"),
                        "java_method": java_method.get("method_name"),
                        "java_class": java_method.get("class_name"),
                        "java_package": java_method.get("package"),
                        "features": {
                            "name_similarity": "high" if op_name == method_name else "medium",
                            "wsdl_parameter_count": wsdl_op.get("features", {}).get("parameter_count", 0),
                            "java_parameter_count": java_method.get("features", {}).get("parameter_count", 0)
                        }
                    }
                    training_examples.append(mapping_example)
        
        return training_examples

    # --- Task Execution Flow ---
    service_name = "{{ params.service_name }}"
    include_wsdl = "{{ params.include_wsdl }}" == "True"
    include_openapi = "{{ params.include_openapi }}" == "True"
    output_path = "{{ params.output_path }}"
    include_java_code = "{{ params.include_java_code }}" == "True"
    
    # Validate service exists in catalog
    service_metadata = validate_service_exists(service_name)
    
    # Load definitions from storage
    definitions = load_service_definitions(service_name, include_wsdl, include_openapi)
    
    # Generate training examples from definitions
    training_examples = generate_training_examples(definitions, service_name)
    
    # If Java code generation is enabled, add those examples
    if include_java_code:
        java_repo_url = "{{ params.java_repo_url }}"
        java_branch = "{{ params.java_branch }}"
        java_build_command = "{{ params.java_build_command }}"
        
        # Checkout and build Java repository
        build_result = checkout_and_build_java_repo(
            service_name=service_name,
            repo_url=java_repo_url,
            branch=java_branch,
            build_command=java_build_command
        )
        
        # Generate training examples from Java classes
        java_examples = generate_training_data_from_java_classes(
            build_result=build_result,
            service_name=service_name
        )
        
        # Combine all examples
        training_examples = training_examples + java_examples
    
    # Store training data
    training_metadata = store_training_data(
        training_examples=training_examples,
        service_name=service_name,
        output_path=output_path
    )

# Instantiate the DAG
generate_training_data_dag_instance = generate_training_data_dag()